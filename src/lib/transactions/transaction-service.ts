/**
 * Transaction Service
 * 
 * Provides atomic operations for task approval, project completion,
 * and invoice generation with rollback capabilities
 */

import { readProject, updateProject } from '../projects-utils';
import { writeTask, readTaskById } from '../project-tasks/hierarchical-storage';
import { saveInvoice } from '../invoice-storage';
import { generateInvoiceWithRetry } from '../invoices/robust-invoice-service';
import { checkAndAutoCompleteProject } from '../project-completion/auto-completion-service';

export interface TransactionStep {
  id: string;
  type: 'task_update' | 'project_update' | 'invoice_generation' | 'notification' | 'custom';
  operation: () => Promise<any>;
  rollback: () => Promise<void>;
  description: string;
  data?: any;
}

export interface TransactionResult {
  success: boolean;
  transactionId: string;
  completedSteps: string[];
  failedStep?: string;
  error?: string;
  rollbackPerformed: boolean;
  results: Record<string, any>;
}

export interface TaskApprovalTransaction {
  taskId: number;
  projectId: number;
  freelancerId: number;
  commissionerId: number;
  taskTitle: string;
  projectTitle: string;
  generateInvoice: boolean;
  invoiceType?: 'completion' | 'milestone';
}

/**
 * Execute task approval with transaction integrity
 */
export async function executeTaskApprovalTransaction(
  params: TaskApprovalTransaction
): Promise<TransactionResult> {
  const transactionId = `task_approval_${params.taskId}_${Date.now()}`;
  
  console.log(`🔄 Starting task approval transaction ${transactionId}...`);
  
  const steps: TransactionStep[] = [];
  const completedSteps: string[] = [];
  const results: Record<string, any> = {};
  
  try {
    // Step 1: Get current task state for rollback
    const originalTask = await readTaskById(params.projectId, params.taskId);
    if (!originalTask) {
      throw new Error(`Task ${params.taskId} not found`);
    }
    
    const originalTaskState = { ...originalTask };
    
    // Step 2: Update task to approved
    steps.push({
      id: 'update_task',
      type: 'task_update',
      operation: async () => {
        const updatedTask = {
          ...originalTask,
          status: 'Approved',
          completed: true,
          rejected: false,
          approvedDate: new Date().toISOString(),
          lastModified: new Date().toISOString()
        };
        
        // Get project creation date for consistent storage
        const project = await readProject(params.projectId);
        await writeTask(updatedTask, project?.createdAt);
        
        return updatedTask;
      },
      rollback: async () => {
        console.log(`🔙 Rolling back task ${params.taskId} to original state...`);
        const project = await readProject(params.projectId);
        await writeTask(originalTaskState, project?.createdAt);
      },
      description: `Approve task ${params.taskId}`,
      data: { taskId: params.taskId, originalState: originalTaskState }
    });
    
    // Step 3: Generate invoice if requested
    if (params.generateInvoice) {
      steps.push({
        id: 'generate_invoice',
        type: 'invoice_generation',
        operation: async () => {
          const invoiceRequest = {
            taskId: params.taskId,
            projectId: params.projectId,
            freelancerId: params.freelancerId,
            commissionerId: params.commissionerId,
            taskTitle: params.taskTitle,
            projectTitle: params.projectTitle,
            invoiceType: params.invoiceType || 'completion'
          };
          
          const invoiceResult = await generateInvoiceWithRetry(invoiceRequest);
          
          if (!invoiceResult.success) {
            throw new Error(`Invoice generation failed: ${invoiceResult.error}`);
          }
          
          return invoiceResult;
        },
        rollback: async () => {
          console.log(`🔙 Rolling back invoice generation...`);
          // Note: In a real system, you'd need to delete the generated invoice
          // For now, we'll log this as a manual cleanup item
          console.warn(`⚠️ Manual cleanup required: Delete invoice for task ${params.taskId}`);
        },
        description: `Generate invoice for task ${params.taskId}`,
        data: { taskId: params.taskId, invoiceType: params.invoiceType }
      });
    }
    
    // Step 4: Check for project auto-completion
    steps.push({
      id: 'check_project_completion',
      type: 'project_update',
      operation: async () => {
        const completionResult = await checkAndAutoCompleteProject(params.projectId);
        return completionResult;
      },
      rollback: async () => {
        // If project was auto-completed, revert it
        console.log(`🔙 Rolling back project completion check...`);
        const project = await readProject(params.projectId);
        if (project && project.status === 'completed') {
          await updateProject(params.projectId, { 
            status: 'ongoing',
            completedAt: undefined
          });
        }
      },
      description: `Check project ${params.projectId} for auto-completion`,
      data: { projectId: params.projectId }
    });
    
    // Execute all steps
    for (const step of steps) {
      try {
        console.log(`⚡ Executing step: ${step.description}`);
        const result = await step.operation();
        results[step.id] = result;
        completedSteps.push(step.id);
        console.log(`✅ Step completed: ${step.id}`);
      } catch (stepError) {
        console.error(`❌ Step failed: ${step.id}`, stepError);
        
        // Rollback all completed steps in reverse order
        await rollbackSteps(steps, completedSteps);
        
        return {
          success: false,
          transactionId,
          completedSteps,
          failedStep: step.id,
          error: stepError instanceof Error ? stepError.message : 'Unknown error',
          rollbackPerformed: true,
          results
        };
      }
    }
    
    console.log(`✅ Transaction ${transactionId} completed successfully`);
    
    return {
      success: true,
      transactionId,
      completedSteps,
      rollbackPerformed: false,
      results
    };
    
  } catch (error) {
    console.error(`❌ Transaction ${transactionId} failed:`, error);
    
    // Rollback any completed steps
    await rollbackSteps(steps, completedSteps);
    
    return {
      success: false,
      transactionId,
      completedSteps,
      error: error instanceof Error ? error.message : 'Unknown error',
      rollbackPerformed: true,
      results
    };
  }
}

/**
 * Execute project completion with transaction integrity
 */
export async function executeProjectCompletionTransaction(
  projectId: number,
  completedBy: number
): Promise<TransactionResult> {
  const transactionId = `project_completion_${projectId}_${Date.now()}`;
  
  console.log(`🔄 Starting project completion transaction ${transactionId}...`);
  
  const steps: TransactionStep[] = [];
  const completedSteps: string[] = [];
  const results: Record<string, any> = {};
  
  try {
    // Get original project state
    const originalProject = await readProject(projectId);
    if (!originalProject) {
      throw new Error(`Project ${projectId} not found`);
    }
    
    const originalProjectState = { ...originalProject };
    
    // Step 1: Update project status to completed
    steps.push({
      id: 'complete_project',
      type: 'project_update',
      operation: async () => {
        await updateProject(projectId, {
          status: 'completed',
          completedAt: new Date().toISOString(),
          completedBy
        });
        return { projectId, status: 'completed' };
      },
      rollback: async () => {
        console.log(`🔙 Rolling back project ${projectId} completion...`);
        await updateProject(projectId, {
          status: originalProjectState.status,
          completedAt: undefined,
          completedBy: undefined
        });
      },
      description: `Complete project ${projectId}`,
      data: { projectId, originalState: originalProjectState }
    });
    
    // Step 2: Generate final invoices if needed
    // This would be expanded based on your business logic
    
    // Execute all steps
    for (const step of steps) {
      try {
        console.log(`⚡ Executing step: ${step.description}`);
        const result = await step.operation();
        results[step.id] = result;
        completedSteps.push(step.id);
        console.log(`✅ Step completed: ${step.id}`);
      } catch (stepError) {
        console.error(`❌ Step failed: ${step.id}`, stepError);
        
        // Rollback all completed steps
        await rollbackSteps(steps, completedSteps);
        
        return {
          success: false,
          transactionId,
          completedSteps,
          failedStep: step.id,
          error: stepError instanceof Error ? stepError.message : 'Unknown error',
          rollbackPerformed: true,
          results
        };
      }
    }
    
    console.log(`✅ Transaction ${transactionId} completed successfully`);
    
    return {
      success: true,
      transactionId,
      completedSteps,
      rollbackPerformed: false,
      results
    };
    
  } catch (error) {
    console.error(`❌ Transaction ${transactionId} failed:`, error);
    
    await rollbackSteps(steps, completedSteps);
    
    return {
      success: false,
      transactionId,
      completedSteps,
      error: error instanceof Error ? error.message : 'Unknown error',
      rollbackPerformed: true,
      results
    };
  }
}

/**
 * Rollback completed steps in reverse order
 */
async function rollbackSteps(
  steps: TransactionStep[], 
  completedSteps: string[]
): Promise<void> {
  console.log(`🔙 Rolling back ${completedSteps.length} completed steps...`);
  
  // Rollback in reverse order
  for (let i = completedSteps.length - 1; i >= 0; i--) {
    const stepId = completedSteps[i];
    const step = steps.find(s => s.id === stepId);
    
    if (step) {
      try {
        console.log(`🔙 Rolling back step: ${step.description}`);
        await step.rollback();
        console.log(`✅ Rollback completed: ${stepId}`);
      } catch (rollbackError) {
        console.error(`❌ Rollback failed for step ${stepId}:`, rollbackError);
        // Continue with other rollbacks even if one fails
      }
    }
  }
  
  console.log(`🔙 Rollback process completed`);
}

/**
 * Execute custom transaction with provided steps
 */
export async function executeCustomTransaction(
  transactionId: string,
  steps: TransactionStep[]
): Promise<TransactionResult> {
  console.log(`🔄 Starting custom transaction ${transactionId}...`);
  
  const completedSteps: string[] = [];
  const results: Record<string, any> = {};
  
  try {
    for (const step of steps) {
      try {
        console.log(`⚡ Executing step: ${step.description}`);
        const result = await step.operation();
        results[step.id] = result;
        completedSteps.push(step.id);
        console.log(`✅ Step completed: ${step.id}`);
      } catch (stepError) {
        console.error(`❌ Step failed: ${step.id}`, stepError);
        
        await rollbackSteps(steps, completedSteps);
        
        return {
          success: false,
          transactionId,
          completedSteps,
          failedStep: step.id,
          error: stepError instanceof Error ? stepError.message : 'Unknown error',
          rollbackPerformed: true,
          results
        };
      }
    }
    
    console.log(`✅ Transaction ${transactionId} completed successfully`);
    
    return {
      success: true,
      transactionId,
      completedSteps,
      rollbackPerformed: false,
      results
    };
    
  } catch (error) {
    console.error(`❌ Transaction ${transactionId} failed:`, error);
    
    await rollbackSteps(steps, completedSteps);
    
    return {
      success: false,
      transactionId,
      completedSteps,
      error: error instanceof Error ? error.message : 'Unknown error',
      rollbackPerformed: true,
      results
    };
  }
}
